import time
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy_json,
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    get_odps_sql_result_as_df,
    get_logger,
)
import os
import re

logger = get_logger("biaoguo")

os.environ["PYTHONIOENCODING"] = "UTF-8"

today = datetime.now().strftime("%Y%m%d")

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
# 多区域爬取

referer = "https://servicewechat.com/wx6fffdc1a67a2acb6/156/page-frame.html"
current_timestamp = int(time.time() * 1000)


region_list = [
    {
        "brand_name": "标果-杭州",
        "region_name": "hangzhou",
        # "headers": {  # headers for general requests related to this region
        #     "token": "d962d0ba06514ffa8b80a335d851563f",
        #     "sid": "7731297",
        #     "referer": referer,
        #     "time": f"{current_timestamp}",
        # },
        "headers": {  # headers specifically for fetching categories in this region
            "token": "e1d1568dbe824d849f7c695e414d0c97",
            "sid": "12394076",
            "referer": referer,
            "time": f"{current_timestamp}",
        },
        "category_fetch_headers": {  # headers specifically for fetching categories in this region
            "token": "e1d1568dbe824d849f7c695e414d0c97",
            "sid": "12394076",
            "referer": referer,
            "time": f"{current_timestamp}",
        },
    },
    {
        "brand_name": "标果-长沙",
        "region_name": "changsha",
        "headers": {
            "token": "5b0c07e2078e46d4b92423e9d0eca1b2",
            "sid": "8200918",
            "referer": referer,
            "time": f"{current_timestamp}",
        },
    },
    {
        "brand_name": "标果-广东",
        "region_name": "guangdong",
        "headers": {
            "token": "80f45c7cb17a4ecbae516d50f9e53caf",
            "sid": "8272073",
            "referer": referer,
            "time": f"{current_timestamp}",
        },
    },
    {
        "brand_name": "标果-川渝",
        "region_name": "sichuan_chongqing",
        "headers": {
            "token": "fe1b98a96f2a40f089de582457a0f4e2",
            "sid": "8823667",
            "referer": referer,
            "time": f"{current_timestamp}",
        },
    },
]
competitor_name_en = "biaoguo"

table_name = f"summerfarm_ds.spider_{competitor_name_en}_with_prop_product_result_df"

logger.info(f"{time_of_now}, region_list:{region_list}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")

pattern = re.compile(r"零食|耗材|买过多次|礼盒")


# 根据三级类目获取商品列表：
def get_products_for_category(categoryId=5745, current=1, headers={}):
    url = "https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods"
    data = {
        "size": 20,
        "current": current,
        "categoryId": categoryId,
        "goodsSourceType": 0,
        "searchSortType": "COMPREHENSIVE",
        "goodsSaleTagId": "",
        "propertyValueIds": [],
    }
    text = get_remote_data_with_proxy(url, headers=headers, json=data)
    try:
        text_json = json.loads(text)
        return text_json["content"]["records"]
    except Exception as e:
        logger.info(f"爬取异常, 标果返回:{text},\n请求的data:{data}")
        return []


# 根据商品ID查询商品详情页信息：
def get_products_detail_for_goods(goods={}, headers={}):
    all_goods_prop = []
    goods_id = goods["id"]
    url = f"https://demeter-api.biaoguoworks.com/leechee/api/h5/store/goods/goods-detail?id={goods_id}"
    result = get_remote_data_with_proxy_json(url, headers=headers, post=True)
    logger.info(f"{result}")
    result = result["content"]
    goods["goodsDetail"] = result

    goodsPropDetailList = result["goodsPropDetailList"]
    for goods_prop in goodsPropDetailList:
        goods_detail_prop = {}
        goods_detail_prop[goods_prop["propertyName"]] = goods_prop["propertyValue"]
        all_goods_prop.append(goods_detail_prop)
    goods["goodsPropDetailList"] = all_goods_prop
    return all_goods_prop


def fetch_for_single_category(category={}, headers={}):
    global region_product_list_all
    global region_product_all
    global region_manual_add_category_product_list_all
    product_of_category = []
    categoryLevel = category["categoryLevel"]
    goodsCount = category["goodsCount"]
    categoryId = category["id"]
    categoryName = category["categoryName"]
    ruleType = category["ruleType"]
    logger.info(f"{categoryName}, categoryId:{categoryId}, goodsCount:{goodsCount}")
    if categoryLevel != 3:
        logger.warn(f"无需爬取:{category}")
        return
    matches = pattern.findall(f"{categoryName}")
    if matches is not None and len(matches) > 0:
        logger.warn(f"这些类目不需要爬取:{pattern}, categoryName:{categoryName}")
        return

    size = 0
    current = 1
    while size < goodsCount:
        logger.info(
            f"current:{current}, size:{size}, goodsCount:{goodsCount}, category:{category}"
        )
        sub_list = get_products_for_category(
            categoryId=categoryId, current=current, headers=headers
        )
        current = current + 1
        if sub_list is None or len(sub_list) <= 0:
            break
        size = size + len(sub_list)
        product_of_category.extend(sub_list)

    if len(product_of_category) <= 0:
        logger.error(f"类目:{categoryName} 没有商品")
        return
    else:
        logger.info(f">>>>>>>>类目:{categoryName}, 商品数:{len(product_of_category)}")
    for product in product_of_category:
        if ruleType == "MANUAL_ADD":
            logger.info(f"这可能是营销类目MANUAL_ADD：{categoryName}")
            product["manual_add_category"] = categoryName
            region_manual_add_category_product_list_all.append(product)
            continue
        if product["skuCode"] in region_product_all:
            logger.info(f"添加过了:{product['skuCode']}")
            continue
        else:
            product["categoryName"] = categoryName
            region_product_list_all.append(product)
            region_product_all[product["skuCode"]] = True


region_manual_add_category_product_list_all = []
region_product_list_all = []
region_product_all = {}


def fetch_for_region(region):
    logger.info(f"即将开始处理:{region}")
    headers = region["headers"]
    category_headers = region.get("category_fetch_headers", headers)
    global region_product_list_all
    global region_manual_add_category_product_list_all
    global region_product_all

    region_product_list_all.clear()
    region_manual_add_category_product_list_all.clear()
    region_product_all = {}
    # 获取所有商品类目：
    category_list = get_remote_data_with_proxy_json(
        "https://demeter-api.biaoguoworks.com/leechee/api/h5/store/front-categorys",
        headers=category_headers,
        json={},
    ).get("content", [])

    logger.info(f"{region} 类目长度:{len(category_list)}")

    category_list_df = pd.DataFrame(category_list)
    level3_category_list_df = category_list_df[category_list_df["categoryLevel"] == 3]
    level3_category_list_df = pd.merge(
        left=level3_category_list_df,
        right=category_list_df[["categoryName", "id", "parentId"]],
        left_on="parentId",
        right_on="id",
        suffixes=["", "_lv2"],
    )
    level3_category_list_df = pd.merge(
        left=level3_category_list_df,
        right=category_list_df[["categoryName", "id", "parentId"]],
        left_on="parentId_lv2",
        right_on="id",
        suffixes=["", "_lv1"],
    )

    level3_category_list_df["categoryName"] = level3_category_list_df.apply(
        lambda row: f"{row['categoryName_lv1']}_{row['categoryName_lv2']}_{row['categoryName']}",
        axis=1,
    )

    logger.info(f"level3_category_list_df:{level3_category_list_df.head(1)}")

    for _, category in level3_category_list_df.iterrows():
        logger.info(
            f"标果3级类目:{category['categoryName']}, ID:{category['id']}, {category.to_dict()}"
        )

    # 多线程
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures_of_category_task = [
            executor.submit(
                fetch_for_single_category,
                category.to_dict(),
                headers,
            )
            for _, category in level3_category_list_df.iterrows()
        ]
        # Wait for all tasks to complete
        concurrent.futures.wait(futures_of_category_task)
        logger.info(f">>>>>>>>>>{region}的类目商品总数:{len(region_product_list_all)}")

        # Submit fetch product detail tasks to the executor
        futures = [
            executor.submit(get_products_detail_for_goods, goods)
            for goods in region_product_list_all
        ]
        # Wait for all tasks to complete
        concurrent.futures.wait(futures)

    logger.info(f"{region}的数据总数:{len(region_product_list_all)}")
    region_product_list_all_df = pd.DataFrame(region_product_list_all)
    # 写入odps
    region_product_list_all_df["competitor"] = region["brand_name"]
    all_products_df = region_product_list_all_df.astype(str)

    partition_spec = f"ds={today},competitor_name={competitor_name_en}"

    result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)
    logger.info(f"{region}写入ODPS的结果:{result}")

    if len(region_manual_add_category_product_list_all) > 0:
        # 营销类目的商品数据
        region_manual_add_category_product_list_all_df = pd.DataFrame(
            region_manual_add_category_product_list_all
        )
        region_manual_add_category_product_list_all_df["competitor"] = region[
            "brand_name"
        ]
        region_manual_add_category_product_list_all_df = (
            region_manual_add_category_product_list_all_df.astype(str)
        )
        manual_table_name = f"summerfarm_ds.spider_{competitor_name_en}_manual_add_category_product_result_df"
        result = write_pandas_df_into_odps(
            region_manual_add_category_product_list_all_df,
            manual_table_name,
            partition_spec,
        )
        logger.info(f"{region}写入ODPS:{manual_table_name}的结果:{result}")

    days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
    df = get_odps_sql_result_as_df(
        f"""select ds,competitor,count(*) as recods 
                                from {table_name}
                                where ds>='{days_30}' group by ds,competitor order by ds desc,competitor limit 50"""
    )
    logger.info(df)
    return len(region_product_list_all_df)


def check_has_data_for_day(ds: str, competitor: str) -> bool:
    df = get_odps_sql_result_as_df(
        f"""select ds,competitor,count(*) as recods 
            from {table_name}
            where ds='{ds}' and competitor='{competitor}'
            group by ds,competitor"""
    )
    if len(df) > 0:
        logger.info(f"数据检查结果:{df.iloc[0].to_dict()}")
        return True
    return False


all_data_holder = {}

ds_df = get_odps_sql_result_as_df(
    f"""SELECT  ds
        ,competitor
        ,COUNT(*) AS records
FROM    summerfarm_ds.spider_biaoguo_with_prop_product_result_df
WHERE   ds = '{today}'
GROUP BY ds,competitor;"""
)

if ds_df is not None and not ds_df.empty:
    for _, row in ds_df.iterrows():
        all_data_holder[row["competitor"]] = row["records"]

logger.info(f"all_data_holder:{all_data_holder}")

result_cnt = 0
region_names = []
for region in region_list:
    competitor = region["brand_name"]
    logger = get_logger(competitor)
    if competitor == "标果-杭州" and competitor in all_data_holder:
        logger.error(f"{competitor}每天都只需要跑一遍，数据已经有了:{region}")
        continue
    region_names.append(competitor)
    result_cnt = result_cnt + fetch_for_region(region)

logger.info(f"===new_record==={','.join(region_names)}, 商品数:{result_cnt}")
