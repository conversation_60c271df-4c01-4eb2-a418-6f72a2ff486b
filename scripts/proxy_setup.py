import requests
import json as jsonlib
import time
from datetime import datetime, timedelta
import pandas as pd
import os
import re
from odps import ODPS, DataFrame
from odps.accounts import StsAccount
import traceback
import logging

app_log_dir = os.environ.get("APP_LOG_DIR", "./")

# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s][%(levelname)s][%(filename)s_%(name)s] - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[
        logging.FileHandler(f"{app_log_dir}/app.log"),  # Logs to a file named 'app.log'
        logging.StreamHandler(),  # Logs to the console
    ],
)


def get_logger(name) -> logging.Logger:
    logger = logging.getLogger(name)
    if not logger.hasHandlers():  # Ensure we don't add multiple handlers
        handler = logging.StreamHandler()
        formatter = logging.Formatter("%(name)s - %(levelname)s - %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
    return logger


import json

logging.info(json.dumps(dict(os.environ), indent=2))

ALIBABA_CLOUD_ACCESS_KEY_ID = os.environ["ALIBABA_CLOUD_ACCESS_KEY_ID"]
ALIBABA_CLOUD_ACCESS_KEY_SECRET = os.environ["ALIBABA_CLOUD_ACCESS_KEY_SECRET"]
THREAD_CNT = int(os.environ.get("THREAD_CNT", 20))
USE_PROXY = bool(os.environ.get("USE_PROXY", True))

print(f"USE_PROXY:{USE_PROXY}")

DEFAULT_MAX_RETRY_NUM = 5

logging.info(f"Thread count: {THREAD_CNT}")

odps = ODPS(
    ALIBABA_CLOUD_ACCESS_KEY_ID,
    ALIBABA_CLOUD_ACCESS_KEY_SECRET,
    project="summerfarm_ds_dev",
    endpoint="http://service.cn-hangzhou.maxcompute.aliyun.com/api",
)


def create_directory_if_not_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)


from datetime import datetime

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
date_of_now = datetime.now().strftime("%Y-%m-%d")


def get_proxy_list_from_server():
    all_proxies_respons = requests.get(
        "http://v2.api.juliangip.com/postpay/getips?auto_white=1&num=10&pt=1&result_type=text&split=1&trade_no=6343123554146908&sign=11c5546b75cde3e3122d05e9e6c056fe",
        verify=False,
        proxies={},
    )
    all_proxies = all_proxies_respons.text
    if not all_proxies_respons.status_code == 200 or '"code":400' in all_proxies:
        logging.info(f"获取代理IP时出错误:{all_proxies}")
        return []
    logging.info(f"获取了新的代理列表:{all_proxies}")
    proxy_list = all_proxies.split("\r\n")
    return proxy_list


import requests
import random

five_min = 2 * 60
PROXY_ERROR_CNT = 0

last_time_fetch_server_list = int(datetime.timestamp(datetime.now()) / five_min)
proxy_list = get_proxy_list_from_server()
proxy_list.extend(get_proxy_list_from_server())
logging.info(proxy_list)

import re


def encode_http_proxy_info(
    proxies={
        "http": "http://18258841203:8gTcEKLs@",
    }
):
    if proxies is None or len(proxies) <= 0 or not "http" in proxies:
        return f"illegal_proxy:{proxies}"
    string = proxies["http"]
    masked_string = re.sub(r"\d{11}", lambda match: "*" * len(match.group()), string)
    return masked_string


def choice_proxy_randomly(proxy_list=proxy_list, retrying_num=0):
    len_of_proxy = len(proxy_list)
    random_number = random.randint(0, len_of_proxy)
    proxy_host_and_port = proxy_list[(retrying_num + random_number) % len_of_proxy]
    logging.info(f"随机的使用一个代理IP:{proxy_host_and_port}")
    return {
        "http": f"http://18258841203:8gTcEKLs@{proxy_host_and_port}",
    }


def get_remote_data_with_proxy_json(
    url,
    json=None,
    data=None,
    post=False,
    headers={},
    cookies={},
    max_retries=DEFAULT_MAX_RETRY_NUM,
    timeout=10,
) -> dict:
    return jsonlib.loads(
        get_remote_data_with_proxy(
            url=url,
            json=json,
            data=data,
            post=post,
            headers=headers,
            cookies=cookies,
            max_retries=max_retries,
            timeout=timeout,
        )
    )


def get_remote_data_with_proxy(
    url,
    json=None,
    data=None,
    post=False,
    headers={},
    cookies={},
    max_retries=DEFAULT_MAX_RETRY_NUM,
    timeout=10,
):
    global last_time_fetch_server_list
    global proxy_list
    global PROXY_ERROR_CNT
    if data is not None:
        json = data

    for i in range(max_retries):
        proxies = {}
        if len(proxy_list) > 0 and USE_PROXY:
            proxies = choice_proxy_randomly(proxy_list=proxy_list, retrying_num=i)
            logging.info(
                f"url:{url}, using proxy: {encode_http_proxy_info(proxies)}, retry_cnt:{i}, headers:{headers}"
            )
        else:
            logging.warning(f"未使用代理:{url}")
        status_code = -1
        try:
            response = None
            if json is not None:
                response = requests.post(
                    url,
                    json=json,
                    proxies=proxies,
                    headers={
                        k: v.encode("utf-8") if isinstance(v, str) else v
                        for k, v in headers.items()
                    },
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                )
            elif post:
                response = requests.post(
                    url,
                    proxies=proxies,
                    headers={
                        k: v.encode("utf-8") if isinstance(v, str) else v
                        for k, v in headers.items()
                    },
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                )
            else:
                response = requests.get(
                    url,
                    proxies=proxies,
                    headers={
                        k: v.encode("utf-8") if isinstance(v, str) else v
                        for k, v in headers.items()
                    },
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                )
            status_code = response.status_code
            if status_code == 200:
                logging.info(
                    f"response status:{response.status_code}, proxy used:{encode_http_proxy_info(proxies)}"
                )
                return response.text
            else:
                logging.warning(f"返回了非200的结果{response.text}")
                raise Exception(
                    f"Error getting data, status: {response.status_code}, text:{response.text}"
                )
        except Exception as e:
            if status_code >= 400 and status_code < 500:
                logging.info(f"http 4xx错误:{status_code}")
                raise e
            time_to_fetch_new_server = int(
                datetime.timestamp(datetime.now()) / five_min
            )
            if time_to_fetch_new_server > last_time_fetch_server_list:
                last_time_fetch_server_list = time_to_fetch_new_server
                proxy_list = get_proxy_list_from_server()
                logging.info(f"new proxy server:{proxy_list}")

            logging.info(
                f"Error getting data:\n{e}\nurl:{url}\nproxy used:{encode_http_proxy_info(proxies)}\nretrying:{i}"
            )
            if i == max_retries - 1:
                logging.info(f"重试已达上限:{max_retries}, 已重试次数:{i+1}")
                raise e

    return None


hints = {"odps.sql.hive.compatible": True, "odps.sql.type.system.odps2": True}


def get_odps_sql_result_as_df(sql) -> pd.DataFrame:
    instance = odps.execute_sql(sql, hints=hints)
    instance.wait_for_success()
    pd_df = None
    with instance.open_reader(tunnel=True) as reader:
        # type of pd_df is pandas DataFrame
        pd_df = reader.to_pandas()

    if pd_df is not None:
        logging.info(f"sql:\n{sql}\ncolumns:{pd_df.columns}")
        return pd_df
    return None


def add_new_column_to_table(table_name, column_name):
    if not "summerfarm_ds." in table_name:
        table_name = f"summerfarm_ds.{table_name}"
    sql = f"ALTER TABLE {table_name} ADD COLUMNS ({column_name} STRING);"
    instance = odps.execute_sql(sql)
    instance.wait_for_success()
    logging.info(f"添加新字段成功:{table_name}, {column_name}")


def ensure_all_df_columns_in_odps_table(df, table_name):
    if not "summerfarm_ds." in table_name:
        table_name = f"summerfarm_ds.{table_name}"
    if not odps.exist_table(table_name):
        logging.info(f"表不存在:{table_name}")
        return True
    table = odps.get_table(table_name)
    column_names = set([column.name for column in table.table_schema])
    column_names_out = ",".join(column_names)
    logging.info(f"DaraFrame字段合集:{column_names_out}")
    df_columns = df.columns.tolist()
    for df_col in df_columns:
        df_col = df_col.lower()
        if not df_col in column_names:
            logging.info(f"新字段:{df_col}, ODPS全部的字段:{column_names}")
            add_new_column_to_table(table_name, df_col)
    return True


def write_pandas_df_into_odps(df, table_name, partition_spec) -> bool:
    if df is None or len(df) <= 0:
        logging.info(f"数据DF为空, table:{table_name}")
        return False
    time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    df["spider_fetch_time"] = time_of_now
    ensure_all_df_columns_in_odps_table(df, table_name)
    exception = None
    for attemp in range(5):
        try:
            odps_df = DataFrame(df)
            odps_df.persist(
                table_name,
                partition=partition_spec,
                drop_partition=False,
                create_partition=True,
                overwrite=False,
                lifecycle=365,
            )
            logging.info(
                f"成功写入odps:{table_name}, partition_spec:{partition_spec}, attemp:{attemp}"
            )
            # 返回true
            return True
        except Exception as e:
            exception = e
            logging.info(f"写入ODPS不成功:{table_name}{e}")
            traceback.print_exc()
            time.sleep(10)
    # 默认返回false
    if exception is not None:
        raise exception
    return False


def camel_to_snake(name):
    """Convert camel case string to snake case string."""
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def rename_columns_to_snake_case(df):
    """Rename DataFrame columns from camel case to snake case."""
    df.rename(columns=lambda x: camel_to_snake(x), inplace=True)


# usage:
# rename_columns_to_snake_case(all_products_df)


class SpiderResultReporter:
    """
    爬虫结果报告器 - 用于统一处理爬虫结果的结构化输出

    这个类提供了一个健壮的方式来输出爬虫结果，替代原有的"===new_record==="标记方式
    """

    def __init__(self, spider_name: str, brand_name: str = ""):
        """
        初始化爬虫结果报告器

        Args:
            spider_name: 爬虫脚本名称（如 "baijian_spider.py"）
            brand_name: 品牌名称（如 "百简"）
        """
        self.spider_name = spider_name
        self.brand_name = brand_name or spider_name.replace("_spider.py", "").replace(".py", "")
        self.start_time = datetime.now()
        self.logger = logging.getLogger(f"SpiderReporter_{spider_name}")

    def report_success(self, product_count: int, additional_info: dict = None):
        """
        报告爬虫成功结果

        Args:
            product_count: 爬取到的商品数量
            additional_info: 额外信息字典，可包含任何需要记录的信息
        """
        end_time = datetime.now()
        duration = int((end_time - self.start_time).total_seconds())

        result = {
            "status": "success",
            "spider_name": self.spider_name,
            "brand_name": self.brand_name,
            "product_count": product_count,
            "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": duration,
            "timestamp": end_time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if additional_info:
            result.update(additional_info)

        # 输出结构化JSON结果（用于脚本解析）
        json_output = jsonlib.dumps(result, ensure_ascii=False)
        print(f"SPIDER_RESULT_JSON:{json_output}")

        # 输出人类可读的日志
        self.logger.info(f"爬虫执行成功 - {self.brand_name}: {product_count}条记录, 耗时{duration}秒")

        return result

    def report_failure(self, error_message: str, error_type: str = "unknown", additional_info: dict = None):
        """
        报告爬虫失败结果

        Args:
            error_message: 错误信息
            error_type: 错误类型（如 "network_error", "parse_error", "auth_error" 等）
            additional_info: 额外信息字典
        """
        end_time = datetime.now()
        duration = int((end_time - self.start_time).total_seconds())

        result = {
            "status": "failure",
            "spider_name": self.spider_name,
            "brand_name": self.brand_name,
            "product_count": 0,
            "error_message": error_message,
            "error_type": error_type,
            "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": duration,
            "timestamp": end_time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if additional_info:
            result.update(additional_info)

        # 输出结构化JSON结果（用于脚本解析）
        json_output = jsonlib.dumps(result, ensure_ascii=False)
        print(f"SPIDER_RESULT_JSON:{json_output}")

        # 输出人类可读的错误日志
        self.logger.error(f"爬虫执行失败 - {self.brand_name}: {error_message} (类型: {error_type}), 耗时{duration}秒")

        return result

    def report_partial_success(self, product_count: int, warning_message: str, additional_info: dict = None):
        """
        报告爬虫部分成功结果（有数据但有警告）

        Args:
            product_count: 爬取到的商品数量
            warning_message: 警告信息
            additional_info: 额外信息字典
        """
        end_time = datetime.now()
        duration = int((end_time - self.start_time).total_seconds())

        result = {
            "status": "partial_success",
            "spider_name": self.spider_name,
            "brand_name": self.brand_name,
            "product_count": product_count,
            "warning_message": warning_message,
            "start_time": self.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": duration,
            "timestamp": end_time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if additional_info:
            result.update(additional_info)

        # 输出结构化JSON结果（用于脚本解析）
        json_output = jsonlib.dumps(result, ensure_ascii=False)
        print(f"SPIDER_RESULT_JSON:{json_output}")

        # 输出人类可读的警告日志
        self.logger.warning(f"爬虫部分成功 - {self.brand_name}: {product_count}条记录, 警告: {warning_message}, 耗时{duration}秒")

        # 为了向后兼容，也输出原有格式
        self.logger.info(f"===new_record==={self.brand_name}, 商品数:{product_count}")

        return result


def create_spider_reporter(spider_name: str, brand_name: str = "") -> SpiderResultReporter:
    """
    创建爬虫结果报告器的便捷函数

    Args:
        spider_name: 爬虫脚本名称
        brand_name: 品牌名称

    Returns:
        SpiderResultReporter实例
    """
    return SpiderResultReporter(spider_name, brand_name)
