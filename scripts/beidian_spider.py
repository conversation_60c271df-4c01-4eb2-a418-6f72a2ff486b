import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
    create_spider_reporter,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {"Referer": "https://servicewechat.com/wx430dcb474584da26/12/page-frame.html","Content-Type": "application/json"}
brand_name = "焙店"
competitor_name_en = "beidian"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("beidian_spider.py", brand_name)

logging.info(time_of_now)

# 登录
url = "https://api.hpsmartcloud.com/jmart/v1.0.3/shoplogin/MemberVerify"
data = {"Account": "***********", "Password": "111111"}
res = requests.post(url, headers=headers, data=json.dumps(data), verify=False)
if not res.headers.get("clerk_sid"):
    print(f"爬虫失败:{res.text}")
    exit(-1)
logging.info(res.headers.get("clerk_sid"))
data = res.json()["Detail"]["ClerkIdentity"]

logging.info(data)

shop_name = data["MemberName"]
ShopRegionPath = data["ShopRegionPath"]
ShopAddress = data["ShopAddress"]

headers["clerk_sid"] = res.headers["clerk_sid"]

logging.info(f"\nnew headers:{headers}, shop_name:{shop_name}, ShopAddress:{ShopAddress}, ShopRegionPath:{ShopRegionPath}")

# 获取一级类目列表

url = f"https://api.hpsmartcloud.com/jmart/v1.0.3/product/Categories?storeKey=8pcba5n2"

categoryList = json.loads(get_remote_data_with_proxy(url, headers=headers))["Detail"]


# 根据一级和二级类目ID爬取商品信息, 返回array
def get_products_for_category(cateID=43, storeKey="8pcba5n2", PageIndex=0):
    url = f"https://api.hpsmartcloud.com/jmart/v1.0.3/product/GetProducts?storeKey={storeKey}"
    res = json.loads(
        get_remote_data_with_proxy(
            url,
            headers=headers,
            data={"PageIndex": PageIndex, "PageSize": 50, "CategoryID": cateID},
        )
    )
    products = res["Detail"]["List"]
    return products


all_product_raw = []
for cate in categoryList:
    PageIndex = 0
    while PageIndex >= 0:
        sub_list = get_products_for_category(
            cateID=cate.get("CategoryID", ""), PageIndex=PageIndex
        )
        for product in sub_list:
            product["CategoryID"] = cate.get("CategoryID", "")
            product["CategoryName"] = cate.get("Name", "")
            product["CategoryNamePath"] = cate.get("NamePath", "")
        logging.info(f"类目:{cate.get('Name', '')} 的商品个数:{len(sub_list)}")
        all_product_raw.extend(sub_list)
        PageIndex = PageIndex + 1
        if len(sub_list) < 50:
            break

logging.info(f"总商品个数:{len(all_product_raw)}, 本次调用返回的第一个商品:{all_product_raw[0]}")

def get_product_detail(product, storeKey="8pcba5n2"):
    logging.info(f"获取商品详情:{product}")
    url = f'https://api.hpsmartcloud.com/jmart/v1.0.3/product/QueryProductCascade?storeKey={storeKey}&productMainID={product["ProductMainID"]}'
    Detail = json.loads(
        get_remote_data_with_proxy(url=url, headers=headers, post=True)
    )["Detail"]
    product["SKU_LIST"] = Detail["SKUs"]
    return Detail["SKUs"]


sku_list_raw = []


def process_product(product):
    product_info = {
        "BaseUnit": product.get("BaseUnit"),
        "Brand": product.get("Brand"),
        "BuySeed": product.get("BuySeed"),
        "CategoryID": product.get("CategoryID"),
        "CategoryName": product.get("CategoryName"),
        "CategoryNamePath": product.get("CategoryNamePath"),
        "Descriptions": product.get("Descriptions"),
        "Details": product.get("Details"),
        "Features": product.get("Features"),
        "FirstCategoryID": product.get("FirstCategoryID"),
        "FirstCategoryName": product.get("FirstCategoryName"),
        "ImageNormal": product.get("ImageNormal"),
        "IsBest": product.get("IsBest"),
        "IsHot": product.get("IsHot"),
        "IsNew": product.get("IsNew"),
        "IsPromotionPrice": product.get("IsPromotionPrice"),
        "IsReturnable": product.get("IsReturnable"),
        "IsSoldOut": product.get("IsSoldOut"),
        "MainSupplierID": product.get("MainSupplierID"),
        "MarketPrice": product.get("MarketPrice"),
        "Name": product.get("Name"),
        "PerUnitPrice": product.get("PerUnitPrice"),
        "PointsInfo": product.get("PointsInfo"),
        "PriceBackUrl": product.get("PriceBackUrl"),
        "ProductMainID": product.get("ProductMainID"),
        "SKUCount": product.get("SKUCount"),
    }
    sku_list = []
    if "SKU_LIST" in product:
        logging.info("已经获取过了")
        sku_list = product["SKU_LIST"]
    else:
        sku_list = get_product_detail(product)
    for sku in sku_list:
        sku_clean = {
            "SkuAddTime": sku.get("AddTime"),
            "SkuBarCode": sku.get("BarCode"),
            "SkuBaseValue": sku.get("BaseValue"),
            "SkuDescriptions": sku.get("Descriptions"),
            "SkuDetails": sku.get("Details"),
            "SkuIsSoldOut": sku.get("IsSoldOut"),
            "SkuLastMonthSales": sku.get("LastMonthSales"),
            "SkuMarketPrice": sku.get("MarketPrice"),
            "SkuMonthSales": sku.get("MonthSales"),
            "SkuMonthViews": sku.get("MonthViews"),
            "SkuOnlineBeginTime": sku.get("OnlineBeginTime"),
            "SkuOnlineEndTime": sku.get("OnlineEndTime"),
            "SkuPerUnitPrice": sku.get("PerUnitPrice"),
            "SkuPoints": sku.get("Points"),
            "SkuPointsInfo": sku.get("PointsInfo"),
            "SkuPriceType": sku.get("PriceType"),
            "SkuProductCode": sku.get("ProductCode"),
            "SkuRetailPrice": sku.get("RetailPrice"),
            "SkuSKUID": sku.get("SKUID"),
            "SkuSaleCount": sku.get("SaleCount"),
            "SkuSalePrice": sku.get("SalePrice"),
            "SkuSalePriceStr": sku.get("SalePriceStr"),
            "SkuShopPrice": sku.get("ShopPrice"),
            "SkuShopPriceStr": sku.get("ShopPriceStr"),
            "SkuSpec": sku.get("Spec"),
            "SkuState": sku.get("State"),
            "SkuStepPrices": sku.get("StepPrices"),
            "SkuStockQuantity": sku.get("StockQuantity"),
            "SkuStockString": sku.get("StockString"),
        }
        sku_clean.update(product_info)
        sku_list_raw.append(sku_clean)


THREAD_CNT = 5
with concurrent.futures.ThreadPoolExecutor(max_workers=THREAD_CNT) as executor:
    # Submit tasks to the executor
    futures = [executor.submit(process_product, product) for product in all_product_raw]

    # 遍历future，获取结果或捕获异常
    for future in concurrent.futures.as_completed(futures):
        try:
            future.result()  # 获取任务结果，如果任务有异常，这里会重新抛出
        except Exception as e:
            logging.error(f"处理商品时发生错误: {e}", exc_info=True)


if len(sku_list_raw) <= 0:
    print(f"爬虫失败了！{shop_name}, {ShopRegionPath}")
    exit(-1)
sku_list_raw_df = pd.DataFrame(sku_list_raw)


sku_list_raw_df["competitor"] = brand_name
raw_data_df = pd.DataFrame(all_product_raw)
raw_data_df["competitor"] = brand_name

sku_list_raw_df["shop_name"] = shop_name
sku_list_raw_df["ShopRegionPath"] = ShopRegionPath
sku_list_raw_df["ShopAddress"] = ShopAddress

raw_data_df["shop_name"] = shop_name
raw_data_df["ShopRegionPath"] = ShopRegionPath
raw_data_df["ShopAddress"] = ShopAddress


sku_list_raw_df = sku_list_raw_df.astype(str)
logging.info(sku_list_raw_df.head(2))
raw_data_df = raw_data_df.astype(str)
today = datetime.now().strftime("%Y%m%d")
days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = "summerfarm_ds.spider_beidian_product_result_df"
raw_table_name = "summerfarm_ds.spider_beidian_product_raw_result_df"

result = write_pandas_df_into_odps(
    sku_list_raw_df, table_name, partition_spec=partition_spec
)
result = result and write_pandas_df_into_odps(
    raw_data_df, raw_table_name, partition_spec=partition_spec
)

df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(1) skus
                                  ,count(distinct ProductMainID)Products
                                  ,count(distinct case when skusaleprice>0 then skuskuid end) has_price_skus
                                  from summerfarm_ds.spider_beidian_product_result_df 
                                  where ds>='{days_30}'
                                  group by ds,competitor_name
                                  order by ds,competitor_name"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(sku_list_raw),
        additional_info={
            "odps_table": table_name,
            "partition": partition_spec,
            "recent_records": str(df) if df is not None else "无历史记录"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name,
            "partition": partition_spec,
            "product_count": len(sku_list_raw) if 'sku_list_raw' in locals() else 0
        }
    )
