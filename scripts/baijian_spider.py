import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    THREAD_CNT,
    create_spider_reporter,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
brand_name = "百简"
competitor_name_en = "baijian"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("baijian_spider.py", brand_name)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
headers = {"domainhost": "cdbj.sdongpo.com"}

# 先登录
url = "https://scm.shudongpoo.com/commonApi/wapData/checkLogin?username=13883824673&password=bj123456&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=0&group_user_id=0&site_id=6&token="
token = requests.get(url, headers=headers, verify=False).json()["data"]["token"]
logging.info(f"login token:{token}")

# 获取一级类目列表

url = f"https://scm.shudongpoo.com/commonApi/wapData/categoryList?version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}"

categoryList = requests.get(url, headers=headers, verify=False).json()["data"][
    "categoryList"
]

# 根据一级类目获取所有二级类目
all_second_category = []
for first_cate in categoryList:
    pid = first_cate["id"]
    first_cate_name = first_cate["name"]
    logging.info(pid)
    url = f"https://scm.shudongpoo.com/commonApi/wapData/categoryList?pid={pid}&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}"
    second_category_list = requests.get(url, headers=headers, verify=False).json()[
        "data"
    ]["categoryList"]
    for second_cate in second_category_list:
        second_cate["一级类目ID"] = pid
        second_cate["一级类目名"] = first_cate_name
        all_second_category.append(second_cate)

logging.info(f"all_second_category:{all_second_category}")


# 根据一级和二级类目ID爬取商品信息, 返回array
def get_products_for_category(cateLevel1Id=298, cateLevel2Id=305):
    url = f"https://scm.shudongpoo.com/commonApi/wapData/goodsList?type_id={cateLevel1Id}&type_id2={cateLevel2Id}&page=1&pageSize=50&page_size=50&version=13.6.0&terminal_type=mini&channel_type=7&op_source=7&terminal_trace_id=9&sub_user_id=0&sales_user_id=1019&group_user_id=0&site_id=6&token={token}"
    products = requests.get(url, headers=headers, verify=False).json()["data"]["list"]
    return products


all_product_raw = []
for cate in all_second_category:
    logging.info(f'{cate["一级类目ID"]}, {cate["id"]}, {cate["name"]}')
    sub_list = get_products_for_category(cate["一级类目ID"], cate["id"])
    logging.info(f"商品个数：{cate['name']},{len(sub_list)}")
    all_product_raw.extend(sub_list)

all_product_raw_df = pd.DataFrame(all_product_raw)
all_product_raw_df.head(1)

# 写入odps
all_product_raw_df["competitor"] = brand_name
all_products_df = all_product_raw_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(all_product_raw),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown",
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown",
            "product_count": len(all_product_raw) if 'all_product_raw' in locals() else 0
        }
    )
