import requests
import json
import pandas as pd
from datetime import datetime, timedelta
import concurrent.futures
from proxy_setup import (
    get_remote_data_with_proxy,
    write_pandas_df_into_odps,
    logging,
    get_odps_sql_result_as_df,
    create_spider_reporter,
)
import os

os.environ["PYTHONIOENCODING"] = "UTF-8"

time_of_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

headers = {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJMb2dpblRpbWUiOjE3MDI1MjA4ODk0OTcsIlRZUEUiOiJYQ1hfQVBQIiwib3BlbmlkIjo3MTI0NTF9.OYU17iehjmF8Wsh53ulodJs1A9ThwvrJk6fcT5FGH4Q",
    "appcodenew": "7798c1f4306b4f89a9fc2a4c2cdc47ac",
    "uid": "712451",
    "time": "*************",
}
brand_name = "藏珍"

# 创建爬虫结果报告器
spider_reporter = create_spider_reporter("cangzhen_spider.py", brand_name)
competitor_name_en = "cangzhen"

logging.info(f"{time_of_now}, headers:{headers}")

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
logging.info(f"time_of_now:{time_of_now},headers:{headers}")
# 获取所有商品列表

# 登录
token_data = {}
data = {
    "accounts_name": "***********",
    "accounts_pass": "767776326c6433397474576a456271314146394346504c55304974353045764b7761472f687330775870706c495845303435463048505153394b734a44326d76695a6b732f654b4e4c4a4b5937744f6462302b39667755445263637a72436a65376c483641694a30715a6b33314936497547666b4c5838364f7372345a6c583070493144313937727358504b4a6d71684c5963354e4f474e6d646564314f574330505773616369434d41343d",
    "login_type": "xcx",
}
try:
    r = requests.post(
        f"https://passport.dhb168.com/login/mUserLogin",
        data=data,
        headers=headers,
        verify=False,
    )
    login_res = r.json()
    token = login_res["token"]
    token_data = {
        "client_id": "3",
        "client_secret": "1a164178e7a4c824303318b2b6f4a0df337b68d6",
        "need_skey": "1",
        "scope": "",
        "code": "undefined",
        "appid": "wx61a903d0ed3feccf",
        "grant_type": "agency_password_sso",
        "token": f"{token}",
        "company_id": "198679",
    }
except Exception as e:
    logging.error(f"登录失败:{brand_name}, {e}")
    raise e

r = requests.post(
    f"https://admin.dhb168.com/Auth/oauth/token", data=token_data, verify=False
).json()
logging.info(r)

# 获取二级类目List
access_token = r["data"]["access_token"]
skey = r["data"]["skey"]

skey_var = f'{{"skey":"{skey}","version":"10.9"}}'
data = {
    "a": "goodsNewCategory",
    "val": f"{skey_var}",
    "c": "DingHuo",
}
logging.info(f"access_token:{access_token}, skey:{skey}")

headers = {"Authorization": f"Bearer {access_token}"}

url = f"https://api.dhb168.com/api.php"

cate_list = requests.post(url, data=data, headers=headers, verify=False).json()["data"]
logging.info(cate_list)


## 遍历二级类目，爬取商品list
data = {
    "a": "goodsList",
    "c": "DingHuo",
    "val": f'{{"page":1,"step":30,"category_id":"27460","type":0,"skey":"{skey}","version":"10.9"}}',
}

second_category = cate_list["second_category"]
second_category_list = []
for category_list in second_category:
    logging.info(f"first category id:{category_list}")
    second_category_list.extend(second_category[category_list])

product_list_all = []
for cate in second_category_list:
    category_id = cate["category_id"]
    category_name = cate["category_name"]
    data = {
        "a": "goodsList",
        "c": "DingHuo",
        "val": f'{{"page":1,"step":30,"category_id":"{category_id}","type":0,"skey":"{skey}","version":"10.9"}}',
    }

    product_list = requests.post(url, data=data, headers=headers, verify=False).json()[
        "data"
    ]["list"]
    logging.info(
        f"category:{category_name}-{category_id}, product count:{len(product_list)}"
    )
    product_list_all.extend(product_list)

product_list_all_df = pd.DataFrame(product_list_all)

# 写入odps
product_list_all_df["competitor"] = brand_name
all_products_df = product_list_all_df.astype(str)

today = datetime.now().strftime("%Y%m%d")
partition_spec = f"ds={today},competitor_name={competitor_name_en}"
table_name = f"summerfarm_ds.spider_{competitor_name_en}_product_result_df"

result = write_pandas_df_into_odps(all_products_df, table_name, partition_spec)

days_30 = (datetime.now() - timedelta(30)).strftime("%Y%m%d")
df = get_odps_sql_result_as_df(
    f"""select ds,competitor_name,count(*) as recods 
                             from {table_name}
                             where ds>='{days_30}' group by ds,competitor_name order by ds desc limit 50"""
)

if result:
    logging.info(f"成功了！{datetime.now()},\n{df}")
    # 使用新的结构化输出方式
    spider_reporter.report_success(
        product_count=len(all_products_df),
        additional_info={
        "odps_table": table_name if 'table_name' in locals() else "unknown",
        "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
else:
    # 报告失败
    spider_reporter.report_failure(
        error_message="写入ODPS失败",
        error_type="odps_write_error",
        additional_info={
            "odps_table": table_name if 'table_name' in locals() else "unknown",
            "partition": partition_spec if 'partition_spec' in locals() else "unknown"
        }
    )
