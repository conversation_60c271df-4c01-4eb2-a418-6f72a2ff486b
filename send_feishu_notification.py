import os
import requests
from datetime import datetime
import argparse
import re

parser = argparse.ArgumentParser()
parser.add_argument(
    "--all_success_result",
    default="baijian_spider.py:::2024-06-28 12:25:41 - INFO - ===new_record===百简, 商品数:17__EOF__",
    help="成功的日志集合, 用 __EOF__ 分割",
)
parser.add_argument(
    "--all_failed_result",
    default="",
    help="失败的日志集合, 用 __EOF__ 分割",
)
parser.add_argument(
    "--time_cost_in_seconds",
    default="21",
    help="任务耗时",
)
args, unknown = parser.parse_known_args()

FEISHU_NOTIFY_TOKEN = os.environ.get("FEISHU_NOTIFY_TOKEN", "4c945cb4-1ab8-450d-bb78-89db0f578cba")
formarter = "%H:%M:%S"
started_at = datetime.now().timestamp()

status = {}
failed_status = {}

# 文件名到中文商家名称的映射
filename_to_brand_mapping = {
    "baijian_spider.py": "百简",
    "baofeng_spider.py": "宝丰",
    "beidian_spider.py": "焙店",
    "beixiaoguo_spider.py": "焙小菓",
    "benlaiguofang_spider.py": "本来果坊",
    "biaoguo_with_prop_spider.py": "标果多地区",
    "cangzhen_spider.py": "藏珍",
    "changyingxianbei.py": "长盈鲜焙",
    "dawosi_spider.py": "达沃斯",
    "dayinyuncang_spider.py": "答音云仓",
    "fengguogong_spider.py": "蜂果供",
    "gucang_spider.py": "谷仓-上海",
    "guosusong_spider.py": "果速送",
    "jiabei_spider.py": "佳焙",
    "juxiangjia_spider.py": "橘祥家",
    "kexinhongbei_spider.py": "可心烘焙",
    "kunyaohongbei_spider.py": "坤耀烘焙原料",
    "lanwei_spider.py": "蓝微",
    "lvshensongguo_spider.py": "绿神送果-东莞",
    "pinshangfang_spider.py": "品尚坊",
    "shuiguojicaicang_spider.py": "水果集采仓",
    "taiguo_spider.py": "钛果-苏皖",
    "tianpin_spider.py": "添品",
    "xianchengpai_spider.py": "鲜成派-成都",
    "yishengxianguo_spider.py": "壹生鲜果-杭州",
    "youzan_spider.py": "优享鲜焙,料料活子",
    "yunguodingzhi_spider.py": "云果定制",
    "zhangyeshipin_spider.py": "张业食品",
    "biaoguo_spider_multi-region_new_app.py": "标果工厂多地区"
}

def get_brand_name_from_filename(filename):
    """从文件名获取中文商家名称"""
    return filename_to_brand_mapping.get(filename, filename.replace("_spider.py", "").replace(".py", ""))


def get_time_info_from_log(
    log_string="2024-06-28 12:25:41 - INFO - ===new_record===百简, 商品数:17",
):
    # Define a regular expression pattern to match the time only
    pattern = r"\d{2}:\d{2}:\d{2}"

    # Search for the pattern in the log string
    match = re.search(pattern, log_string)

    # Extract the time information
    if match:
        time_info = match.group()
        return time_info
    else:
        print("No time information found in the string.")
        return "00:00:00"


def format_log_for_display(log_content):
    """
    格式化日志内容以便在飞书消息中更好地展示
    - 处理过长的日志内容
    - 美化显示格式
    - 提取关键错误信息
    """
    if not log_content or log_content.strip() == "":
        return "无日志信息"

    # 如果日志内容只是 "File"，说明是占位符
    if log_content.strip() == "File":
        return "日志文件过大或无法读取"

    # 限制日志长度，避免消息过长
    max_length = 200
    if len(log_content) > max_length:
        # 尝试找到最后一个完整的错误信息
        truncated = log_content[:max_length]
        last_bracket = truncated.rfind(']')
        if last_bracket > 0:
            truncated = log_content[:last_bracket + 1]
        log_content = truncated + "..."

    # 移除多余的时间戳和日志级别前缀，保留关键信息
    lines = log_content.split('\n')
    cleaned_lines = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 提取ERROR和WARNING级别的关键信息
        if '[ERROR]' in line or '[WARNING]' in line:
            # 提取错误消息部分
            if '] - ' in line:
                message_part = line.split('] - ', 1)[-1]
                cleaned_lines.append(f"❌ {message_part}")
            else:
                cleaned_lines.append(f"❌ {line}")
        elif '[INFO]' in line and ('登陆信息' in line or '获取到的' in line or 'category_list' in line):
            # 保留重要的INFO信息
            if '] - ' in line:
                message_part = line.split('] - ', 1)[-1]
                cleaned_lines.append(f"ℹ️ {message_part}")

        # 限制行数，避免消息过长
        if len(cleaned_lines) >= 3:
            break

    if not cleaned_lines:
        # 如果没有提取到关键信息，返回原始内容的简化版本
        return log_content.replace('\n', ' ')[:100] + ("..." if len(log_content) > 100 else "")

    return '\n        '.join(cleaned_lines)


# 处理成功结果 - 支持新的结构化数据格式
# 数据格式：brand_name:::count, timestamp
# 这个格式由run_all_docker.sh脚本生成，兼容新的SpiderResultReporter输出
if args.all_success_result is not None and len(args.all_success_result) > 1:
    for success_result in args.all_success_result.split("__EOF__"):
        print("success_result", success_result)
        if len(success_result) <= 5:
            print("this should be the last one.")
            continue

        file_result = f"{success_result}".split(":::")
        print("file_result:", file_result)

        # 新格式：brand_name:::count, timestamp
        # file_result[0] = brand_name
        # file_result[1] = "count, timestamp"
        if len(file_result) >= 2:
            brand_name = file_result[0].strip()
            count_and_time = file_result[1].strip()  # 例如: "51, 2025-06-23 03:32:28"

            # 清理品牌名称，去除可能的异常后缀
            if brand_name:
                # 去除可能的SPIDER_RESULT_JSON:后缀和换行符
                brand_name = re.sub(r'SPIDER_RESULT_JSON:.*$', '', brand_name, flags=re.DOTALL)
                brand_name = re.sub(r'\\n.*$', '', brand_name)  # 处理转义的换行符
                brand_name = brand_name.replace('\\n', '').replace('\\r', '').replace('\n', '').replace('\r', '')
                brand_name = re.sub(r'\s+', ' ', brand_name).strip()

            # 解析数量和时间
            if ", " in count_and_time:
                parts = count_and_time.split(", ", 1)
                record_count = parts[0].strip()
                finished_time = parts[1].strip() if len(parts) > 1 else ""

                # 清理完成时间，去除可能的异常后缀
                finished_time = re.sub(r'SPIDER_RESULT_JSON:.*$', '', finished_time, flags=re.DOTALL)
                finished_time = re.sub(r'\\n.*$', '', finished_time)  # 处理转义的换行符
                finished_time = finished_time.replace('\\n', '').replace('\\r', '').replace('\n', '').replace('\r', '')
                finished_time = finished_time.strip()
            else:
                record_count = count_and_time.strip()
                finished_time = ""

            # 验证记录数是否为数字
            try:
                int(record_count)
                print(f"{brand_name}, 记录数:{record_count}, 完成时间:{finished_time}")
                status[brand_name] = f"{record_count}条记录, {finished_time}"
            except ValueError:
                print(f"警告: 记录数不是有效数字: {record_count}, 品牌: {brand_name}")
                status[brand_name] = f"数据异常, {finished_time}"
        else:
            print(f"警告: 无法解析成功结果格式: {success_result}")
            # 兜底处理
            status[success_result] = "解析失败"

# 处理失败结果 - 支持新的结构化数据格式
# 数据格式：file_name:::error_message, timestamp|||log_info
# 这个格式由run_all_docker.sh脚本生成，兼容新的SpiderResultReporter输出
if args.all_failed_result is not None and len(args.all_failed_result) > 1:
    print(f"失败的文件列表:{args.all_failed_result}")
    for failed_result in args.all_failed_result.split("__EOF__"):
        print("failed_result:", failed_result)
        if len(failed_result) <= 5:
            print("this should be the last one.")
            continue
        if ":::" in failed_result:
            file_result = f"{failed_result}".split(":::")
            if len(file_result) >= 2:
                file_name = file_result[0].strip()
                error_and_log = file_result[1].strip()  # 例如: "未爬取到数据, 2025-06-24 02:22:45|||异常信息"

                # 从文件名获取中文商家名称
                brand_name = get_brand_name_from_filename(file_name)

                # 检查是否包含日志信息（用|||分隔）
                if "|||" in error_and_log:
                    error_part, log_part = error_and_log.split("|||", 1)
                    # 格式化失败信息，包含日志
                    formatted_log = format_log_for_display(log_part.strip())
                    failed_status[brand_name] = f"{error_part.strip()}\n    📄 日志: {formatted_log}"
                else:
                    # 没有日志信息，只显示错误
                    failed_status[brand_name] = error_and_log.strip()
            else:
                print(f"警告: 无法解析失败结果格式: {failed_result}")
                # 尝试从失败结果中提取文件名
                brand_name = get_brand_name_from_filename(failed_result)
                failed_status[brand_name] = "解析失败"
        else:
            print(f"警告: 失败结果格式不正确，缺少分隔符: {failed_result}")
            # 尝试从失败结果中提取文件名
            brand_name = get_brand_name_from_filename(failed_result)
            failed_status[brand_name] = "格式错误"


print(failed_status, status)

# https://open.feishu.cn/open-apis/bot/v2/hook/4c945cb4-1ab8-450d-bb78-89db0f578cba
print(
    f"status:{status},  failed_status:{failed_status}, time cost:{args.time_cost_in_seconds}s"
)

text_content = []
# 添加成功的爬虫信息，包含爬虫名称
for key, value in status.items():
    text_content.append(f"- {key}: {value}\n")

# 添加失败的爬虫信息
if len(failed_status.items()) > 0:
    text_content.append("\n\n**以下爬虫失败:**\n")
    for key, value in failed_status.items():
        text_content.append(f"- {key}, {value}\n")


def send_feishu_notice_with_title_and_content(feishu_url, title, markdown_content):
    headers = {"Content-Type": "application/json"}
    data = {
        "msg_type": "interactive",
        "card": {
            "header": {
                "template": "blue",
                "title": {
                    "content": f"**{title}**",
                    "tag": "lark_md",
                },
            },
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_content,
                }
            ],
        },
    }
    feishu_result = requests.post(
        url=feishu_url, json=data, headers=headers, verify=False, proxies={}
    ).json()
    return feishu_result


time_cost = int(args.time_cost_in_seconds)

markdown_content = f"{''.join(text_content)}\n\n**任务耗时:** {int(time_cost/3600)}时{int((time_cost%3600)/60)}分{time_cost%60}秒"

print(f"markdown_content:{markdown_content}")

if len(FEISHU_NOTIFY_TOKEN) > 5:
    # 通知飞书
    headers = {"Content-Type": "application/json"}

    # Post the data
    url = f"https://open.feishu.cn/open-apis/bot/v2/hook/{FEISHU_NOTIFY_TOKEN}"
    feishu_result = send_feishu_notice_with_title_and_content(
        feishu_url=url,
        title=f"成功爬取了{len(status.items())}家竞对数据",
        markdown_content=markdown_content,
    )
    print(f"通知飞书结果:{feishu_result}")
else:
    print(f"无需飞书通知:{FEISHU_NOTIFY_TOKEN}")
